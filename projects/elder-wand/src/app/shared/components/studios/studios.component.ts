import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface Studio {
  id: number;
  title: string;
  description: string;
  image: string;
  link: string;
}

@Component({
  selector: 'app-studios',
  templateUrl: './studios.component.html',
  styleUrls: ['./studios.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule],
})
export class StudiosComponent {
  studios: Studio[] = [
    {
      id: 1,
      title: 'Experience Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: '/experience_studio01.svg',
      link: '/experience',
    },
    {
      id: 2,
      title: 'Product Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: '/product_studio01.svg',
      link: '/product',
    },
    {
      id: 3,
      title: 'Data Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: '/data_studio01.svg',
      link: '/data',
    },
    {
      id: 4,
      title: 'Quality Engineering',
      description: 'Evaluating design elements for accuracy and consistency',
      image: '/quality_engineering01.svg',
      link: '/quality',
    },
    {
      id: 5,
      title: 'FinOps Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: '/finops_studio01.svg',
      link: '/finops',
    },
    {
      id: 6,
      title: 'Platform',
      description: 'Evaluating design elements for accuracy and consistency',
      image: '/platform_studio01.svg',
      link: '/platform',
    },
  ];
}
