<div class="studios-container">
  <div class="studios-header">
    <div class="title-wrapper">
      <span class="sparkle">✨</span>
      <h1>Our <span class="gradient-text">Studios</span></h1>
    </div>
  </div>

  <div class="studios-grid">
    <div
      class="studio-card"
      *ngFor="let studio of studios"
      [routerLink]="studio.link"
    >
      <div class="card-content">
        <div class="text-content">
          <h2>{{ studio.title }}</h2>
          <p class="description">{{ studio.description }}</p>
          <div class="arrow-button">
            <img src="/arrow_studios.svg" alt="Arrow Icon" />
          </div>
        </div>
        <div class="image-container">
          <img [src]="studio.image" [alt]="studio.title" />
        </div>
      </div>
    </div>
  </div>
</div>
