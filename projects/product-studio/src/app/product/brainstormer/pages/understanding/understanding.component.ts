import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component } from '@angular/core';
import { CaptionComponent, IconsComponent } from '@awe/play-comp-library'; // Assuming this is your awe-caption
import { AweCardComponent } from "../../components/awe-card/awe-card.component"; // Adjust path
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';

export interface CanvasItem {
  id: string;
  title: string;
  icon: string;
  iconBg: string;
  data: string[];
}

@Component({
  selector: 'app-understanding',
  templateUrl: './understanding.component.html',
  styleUrls: ['./understanding.component.scss'],
  standalone: true,
  imports: [CommonModule, CaptionComponent, AweCardComponent, IconsComponent, AweModalComponent],
})
export class UnderstandingComponent {
  // Action Icons (used in the projected header)
  trashIcon: string = '/icons/awe_trash.svg';
  editIcon: string = '/icons/awe_edit.svg';
  errorIcon: string = '/icons/awe_error.svg';
  // threeDotsIcon: string = '/icons/three-dot.svg';


  // Card Data Icons (used in the projected header)
  problemIcon: string = '/cards-icons/problem.svg';
  solutionIcon: string = '/cards-icons/solution.svg';
  keyPartnerIcon: string = '/cards-icons/key-pattern.svg';
  valuePropositionIcon: string = '/cards-icons/value-proposition.svg';
  customerSegmentsIcon: string = '/cards-icons/customer-segments.svg';
  keyMetricsIcon: string = '/cards-icons/key-metrics.svg';
  alternativesIcon: string = '/cards-icons/alternatives.svg';
  costStructureIcon: string = '/cards-icons/cost-structure.svg';
  revenueStreamsIcon: string = '/cards-icons/revenue-streams.svg';

  // Main data source for the cards
  businessModelCanvas: CanvasItem[] = [
    {
      id: 'problem', 
      title: 'Problem',
      icon: this.problemIcon,
      iconBg: '#ffebee',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      id: 'key-partners',
      title: 'Key partners',
      icon: this.keyPartnerIcon,
      iconBg: '#e3f2fd',
      data: Array(6).fill('Dummy data for wireframe design'),
    },
    {
      id: 'value-proposition',
      title: 'Value Proposition',
      icon: this.valuePropositionIcon,
      iconBg: '#fff3e0',
      data: Array(6).fill('Dummy data for wireframe design'),
    },
    {
      id: 'customer-segments',
      title: 'Customer Segments',
      icon: this.customerSegmentsIcon,
      iconBg: '#f3e5f5',
      data: Array(6).fill('Dummy data for wireframe design'),
    },
    {
      id: 'key-metrics',
      title: 'Key Metrics',
      icon: this.keyMetricsIcon,
      iconBg: '#e8eaf6',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      id: 'solution',
      title: 'Solution',
      icon: this.solutionIcon,
      iconBg: '#e8f5e9',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      id: 'alternatives',
      title: 'Alternatives',
      icon: this.alternativesIcon,
      iconBg: '#ede7f6',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      id: 'cost-structure',
      title: 'Cost Structure',
      icon: this.costStructureIcon,
      iconBg: '#fce4ec',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      id: 'revenue-streams',
      title: 'Revenue Streams',
      icon: this.revenueStreamsIcon,
      iconBg: '#e0f2f1',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
  ];
   // Modal State
  isEditModalOpen = false;
  selectedItemForEdit: CanvasItem | null = null;
  // Create a working copy for editing to avoid direct mutation until save
  editableItemData: string[] = [];
  regeneratePrompt: string = '';
   constructor(private cdRef: ChangeDetectorRef) {} // Inject ChangeDetectorRef

  get firstRowFirstColumn(): CanvasItem[] {
    return [this.businessModelCanvas[0], this.businessModelCanvas[4]]; // Problem, Key Metrics
  }

  get firstRowSecondColumn(): CanvasItem[] {
    return [
      this.businessModelCanvas[1],
      this.businessModelCanvas[2],
      this.businessModelCanvas[3],
    ]; // Key partners, Value Proposition, Customer Segments
  }

  get firstRowThirdColumn(): CanvasItem[] {
    return [this.businessModelCanvas[5], this.businessModelCanvas[6]]; // Solution, Alternatives
  }

  get secondRowItems(): CanvasItem[] {
    return [this.businessModelCanvas[7], this.businessModelCanvas[8]]; // Cost Structure, Revenue Streams
  }

  // Action handlers
  onEdit(item: CanvasItem): void {
    console.log('Edit:', item.title);
    // Implement edit functionality
  }

  onDelete(item: CanvasItem): void {
    console.log('Delete:', item.title);
    // Implement delete functionality
  }

  // Modal Methods
  openEditModal(item: CanvasItem): void {
    // Create a deep copy for editing data points, or at least a shallow copy of the array
    this.selectedItemForEdit = item;
    this.editableItemData = [...item.data]; // Copy data array for editing
    this.regeneratePrompt = ''; // Reset prompt
    this.isEditModalOpen = true;
  }

  closeEditModal(): void {
    this.isEditModalOpen = false;
    this.selectedItemForEdit = null;
    this.editableItemData = [];
  }

  updateUnderstandingItem(): void {
    if (this.selectedItemForEdit) {
      // Find the original item in the businessModelCanvas and update its data
      const originalItem = this.businessModelCanvas.find(
        (canvasItem) => canvasItem.id === this.selectedItemForEdit!.id
      );
      if (originalItem) {
        originalItem.data = [...this.editableItemData]; // Update with the edited data
        console.log('Updated Item:', originalItem);
      }
      // Potentially handle regeneratePrompt here
      if(this.regeneratePrompt){
        console.log('Regenerate with prompt:', this.regeneratePrompt);
        // Call API or service to regenerate data based on prompt
        // For now, let's just clear it
        this.regeneratePrompt = '';
      }
      this.closeEditModal();
    }
  }

  // Methods for managing editableItemData in the modal
  addEditableDataItem(): void {
    this.editableItemData.push(''); // Add a new empty string to edit
    // Optional: Scroll to the new input or focus it
    this.cdRef.detectChanges(); // Ensure ngFor updates
    setTimeout(() => {
      const inputs = document.querySelectorAll('.edit-data-item-input');
      const lastInput = inputs[inputs.length -1] as HTMLInputElement;
      if (lastInput) {
        lastInput.focus();
      }
    });
  }

  removeEditableDataItem(index: number): void {
    this.editableItemData.splice(index, 1);
  }

  // Required for ngFor with [(ngModel)] on primitive types like string
  trackByFn(index: number, item: any): any {
    return index;
  }

}