<div class="container-fluid p-3">
  <!-- First Row -->
  <div class="row canvas-row">
    <!-- First Column - 2 cards stacked -->
    <div class="col-lg-3 col-md-12 col-sm-12 mb-3">
      <div class="column-container first-column">
        <awe-card *ngFor="let item of firstRowFirstColumn" [showHeader]="true" [showBody]="true"
          [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-canvas-item">
          <!-- Projected Header Content -->
          <div awe-card-header-content class="custom-card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg">
                <img class="card-icon-img fs-5" [src]="item.icon" [alt]="item.title" />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">
              <!-- <button class="icon-button">
                <img [src]="threeDotsIcon" alt="menu" />
              </button> -->

              <awe-icons iconName="three-dot" class="pt-2" role="button" tabindex="0" iconColor="blue"></awe-icons>
            </div>
          </div>

          <!-- Projected Body Content (Default Slot) -->
          <ul class="list-unstyled mb-0">
            <li *ngFor="let dataItem of item.data; let i = index" class="mb-2 d-flex align-items-start">
              <span class="me-2 text-muted">{{ i + 1 }}.</span>
              <awe-caption variant="s2" type="regular">
                {{ dataItem }}
              </awe-caption>
            </li>
          </ul>
        </awe-card>
      </div>
    </div>

    <!-- Second Column - 3 cards -->
    <div class="col-lg-6 col-md-12 col-sm-12 mb-3">
      <div class="column-container second-column">
        <awe-card *ngFor="let item of firstRowSecondColumn" [showHeader]="true" [showBody]="true"
          [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-canvas-item">
          <!-- Projected Header Content -->
          <div awe-card-header-content class="custom-card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg">
                <img class="card-icon-img fs-5" [src]="item.icon" [alt]="item.title" />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">

              <awe-icons iconName="three-dot" class="pt-2" role="button" tabindex="0" iconColor="blue"></awe-icons>
            </div>
          </div>

          <!-- Projected Body Content (Default Slot) -->
          <ul class="list-unstyled mb-0">
            <li *ngFor="let dataItem of item.data; let i = index" class="mb-2 d-flex align-items-start">
              <span class="me-2 text-muted">{{ i + 1 }}.</span>
              <awe-caption variant="s2" type="regular">
                {{ dataItem }}
              </awe-caption>
            </li>
          </ul>
        </awe-card>
      </div>
    </div>

    <!-- Third Column - 2 cards stacked -->
    <div class="col-lg-3 col-md-12 col-sm-12 mb-3">
      <div class="column-container third-column">
        <awe-card *ngFor="let item of firstRowThirdColumn" [showHeader]="true" [showBody]="true"
          [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-canvas-item">
          <!-- Projected Header Content -->
          <div awe-card-header-content class="custom-card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg">
                <img class="card-icon-img fs-5" [src]="item.icon" [alt]="item.title" />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">

              <awe-icons iconName="three-dot" class="pt-2" role="button" tabindex="0" iconColor="blue"></awe-icons>

            </div>
          </div>

          <!-- Projected Body Content (Default Slot) -->
          <ul class="list-unstyled mb-0">
            <li *ngFor="let dataItem of item.data; let i = index" class="mb-2 d-flex align-items-start">
              <span class="me-2 text-muted">{{ i + 1 }}.</span>
              <awe-caption variant="s2" type="regular">
                {{ dataItem }}
              </awe-caption>
            </li>
          </ul>
        </awe-card>
      </div>
    </div>
  </div>

  <!-- Second Row - 2 cards side by side -->
  <div class="row canvas-row">
    <!-- The *ngFor is on the column div, and awe-card is inside it -->
    <div class="col-lg-6 col-md-6 col-sm-12 mb-3" *ngFor="let item of secondRowItems">
      <awe-card [showHeader]="true" [showBody]="true" [applyHeaderPadding]="true" [applyBodyPadding]="true"
        cardClass="understanding-canvas-item">
        <!-- Projected Header Content -->
        <div awe-card-header-content class="custom-card-header d-flex justify-content-between align-items-center">
          <div class="d-flex align-items-center">
            <div class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
              [style.background-color]="item.iconBg">
              <img class="card-icon-img fs-5" [src]="item.icon" [alt]="item.title" />
            </div>
            <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
          </div>
          <div class="card-actions">

            <awe-icons iconName="three-dot" class="pt-2" role="button" tabindex="0" iconColor="blue"></awe-icons>

          </div>
        </div>

        <!-- Projected Body Content (Default Slot) -->
        <ul class="list-unstyled mb-0">
          <li *ngFor="let dataItem of item.data; let i = index" class="mb-2 d-flex align-items-start">
            <span class="me-2 text-muted">{{ i + 1 }}.</span>
            <awe-caption variant="s2" type="regular">
              {{ dataItem }}
            </awe-caption>
          </li>
        </ul>
      </awe-card>
    </div>
  </div>
</div>