<div class="container-fluid p-3">
  <!-- First Row -->
  <div class="row canvas-row">
    <!-- First Column - 2 cards stacked -->
    <div class="col-lg-3 col-md-12 col-sm-12 mb-3">
      <div class="column-container first-column">
        <awe-card *ngFor="let item of firstRowFirstColumn" [showHeader]="true" [showBody]="true"
          [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-canvas-item">
          <!-- Projected Header Content -->
          <div awe-card-header-content class="custom-card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg">
                <img class="card-icon-img fs-5" [src]="item.icon" [alt]="item.title" />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">
              <!-- <button class="icon-button">
                ˀ<img [src]="threeDotsIcon" alt="menu" />
              </button> -->

              <awe-icons iconName="three-dot" class="pt-2" role="button" tabindex="0" iconColor="blue"></awe-icons>
            </div>
          </div>

          <!-- Projected Body Content (Default Slot) -->
          <ul class="list-unstyled mb-0">
            <li *ngFor="let dataItem of item.data; let i = index" class="mb-2 d-flex align-items-start">
              <span class="me-2 text-muted">{{ i + 1 }}.</span>
              <awe-caption variant="s2" type="regular">
                {{ dataItem }}
              </awe-caption>
            </li>
          </ul>
        </awe-card>
      </div>
    </div>

    <!-- Second Column - 3 cards -->
    <div class="col-lg-6 col-md-12 col-sm-12 mb-3">
      <div class="column-container second-column">
        <awe-card *ngFor="let item of firstRowSecondColumn" [showHeader]="true" [showBody]="true"
          [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-canvas-item">
          <!-- Projected Header Content -->
          <div awe-card-header-content class="custom-card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg">
                <img class="card-icon-img fs-5" [src]="item.icon" [alt]="item.title" />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">

              <awe-icons iconName="three-dot" class="pt-2" role="button" tabindex="0" iconColor="blue"></awe-icons>
            </div>
          </div>

          <!-- Projected Body Content (Default Slot) -->
          <ul class="list-unstyled mb-0">
            <li *ngFor="let dataItem of item.data; let i = index" class="mb-2 d-flex align-items-start">
              <span class="me-2 text-muted">{{ i + 1 }}.</span>
              <awe-caption variant="s2" type="regular">
                {{ dataItem }}
              </awe-caption>
            </li>
          </ul>
        </awe-card>
      </div>
    </div>

    <!-- Third Column - 2 cards stacked -->
    <div class="col-lg-3 col-md-12 col-sm-12 mb-3">
      <div class="column-container third-column">
        <awe-card *ngFor="let item of firstRowThirdColumn" [showHeader]="true" [showBody]="true"
          [applyHeaderPadding]="true" [applyBodyPadding]="true" cardClass="understanding-canvas-item">
          <!-- Projected Header Content -->
          <div awe-card-header-content class="custom-card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg">
                <img class="card-icon-img fs-5" [src]="item.icon" [alt]="item.title" />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">

              <awe-icons iconName="three-dot" class="pt-2" role="button" tabindex="0" iconColor="blue"></awe-icons>

            </div>
          </div>

          <!-- Projected Body Content (Default Slot) -->
          <ul class="list-unstyled mb-0">
            <li *ngFor="let dataItem of item.data; let i = index" class="mb-2 d-flex align-items-start">
              <span class="me-2 text-muted">{{ i + 1 }}.</span>
              <awe-caption variant="s2" type="regular">
                {{ dataItem }}
              </awe-caption>
            </li>
          </ul>
        </awe-card>
      </div>
    </div>
  </div>

  <!-- Second Row - 2 cards side by side -->
  <div class="row canvas-row">
    <!-- The *ngFor is on the column div, and awe-card is inside it -->
    <div class="col-lg-6 col-md-6 col-sm-12 mb-3" *ngFor="let item of secondRowItems">
      <awe-card [showHeader]="true" [showBody]="true" [applyHeaderPadding]="true" [applyBodyPadding]="true"
        cardClass="understanding-canvas-item">
        <!-- Projected Header Content -->
        <div awe-card-header-content class="custom-card-header d-flex justify-content-between align-items-center">
          <div class="d-flex align-items-center">
            <div class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
              [style.background-color]="item.iconBg">
              <img class="card-icon-img fs-5" [src]="item.icon" [alt]="item.title" />
            </div>
            <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
          </div>
          <div class="card-actions">

            <awe-icons iconName="three-dot" class="pt-2" role="button" tabindex="0" iconColor="blue"></awe-icons>

          </div>
        </div>

        <!-- Projected Body Content (Default Slot) -->
        <ul class="list-unstyled mb-0">
          <li *ngFor="let dataItem of item.data; let i = index" class="mb-2 d-flex align-items-start">
            <span class="me-2 text-muted">{{ i + 1 }}.</span>
            <awe-caption variant="s2" type="regular">
              {{ dataItem }}
            </awe-caption>
          </li>
        </ul>
      </awe-card>
    </div>
  </div>

  <awe-modal
    [isOpen]="isEditModalOpen"
    (closed)="closeEditModal()"
    [showHeader]="true"
    [showFooter]="true"
    width="600px" 
    height="auto"
    position="center"
    animation="fade"
    [showCloseButton]="true"
    modalClass="edit-understanding-modal"
  >
    <!-- Projected Modal Header -->
    <div awe-modal-header class="edit-modal-header">
      <h5 class="modal-title mb-0">Edit Understanding</h5>
      <!-- awe-modal already adds a close button if showCloseButton is true -->
    </div>

    <!-- Projected Modal Body -->
    <div awe-modal-body *ngIf="selectedItemForEdit" class="edit-modal-body">
      <h6 class="item-title mb-3">{{ selectedItemForEdit.title }}</h6>

      <!-- Editable Data Items -->
      <div *ngFor="let dataPoint of editableItemData; let i = index; trackBy: trackByFn" class="editable-data-item d-flex align-items-center mb-2">
        <input
          type="text"
          class="form-control form-control-sm flex-grow-1 edit-data-item-input"
          [(ngModel)]="editableItemData[i]"
          placeholder="Enter data"
        />
        <button type="button" class="btn btn-sm btn-outline-danger ms-2" (click)="removeEditableDataItem(i)">
          <awe-icons iconName="trash" iconSize="16px"></awe-icons> <!-- Assuming awe-icons can take size -->
        </button>
      </div>

      <!-- Add New Data Item -->
      <button type="button" class="btn btn-sm btn-outline-secondary w-100 add-new-data-btn" (click)="addEditableDataItem()">
        Add New
        <awe-icons iconName="plus" iconSize="16px" class="ms-1"></awe-icons>
      </button>

      <!-- Regenerate Section -->
      <div class="regenerate-section mt-4">
        <label for="regeneratePrompt" class="form-label fw-medium">Regenerate</label>
        <div class="input-group">
          <input
            type="text"
            id="regeneratePrompt"
            class="form-control form-control-sm"
            [(ngModel)]="regeneratePrompt"
            placeholder="Type your prompt here..."
          />
          <button class="btn btn-sm btn-outline-secondary" type="button" (click)="updateUnderstandingItem()"> <!-- Or a dedicated regenerate function -->
             <awe-icons iconName="send" iconSize="16px"></awe-icons> <!-- Placeholder for send icon -->
          </button>
        </div>
      </div>
    </div>

    <!-- Projected Modal Footer -->
    <div awe-modal-footer class="edit-modal-footer">
      <!-- <button type="button" class="btn btn-secondary" (click)="closeEditModal()">Cancel</button> --> <!-- awe-modal handles close via X or backdrop -->
      <button type="button" class="btn btn-primary update-btn" (click)="updateUnderstandingItem()">Update</button>
    </div>
  </awe-modal>
</div>