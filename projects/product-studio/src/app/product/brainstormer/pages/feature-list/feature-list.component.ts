import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { CaptionComponent, HeadingComponent } from '@awe/play-comp-library';
import { EditDialogComponent, DialogConfig, DropdownItem } from '../../components/edit-dialog/edit-dialog.component';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';

interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
}

interface FeatureSection {
  id: string;
  title: string;
  subtitle: string;
  features: FeatureCard[];
}

@Component({
  selector: 'app-feature-list',
  standalone: true,
  imports: [CommonModule, DragDropModule, HeadingComponent, EditDialogComponent, AweCardComponent,CaptionComponent],
  templateUrl: './feature-list.component.html',
  styleUrl: './feature-list.component.scss'
})
export class FeatureListComponent implements OnInit {
  roboBallIcon: string = '/icons/robo_ball.svg';
  threeDotsIcon: string = 'icons/three-dot.svg';

  // Dialog properties
  isDialogVisible: boolean = false;
  dialogConfig: DialogConfig = {
    showHeader: true,
    showFooter: true,
    showCloseButton: true,
    title: 'Edit Feature List',
    width: '600px',
    backdrop: true
  };

  dropdownItems: DropdownItem[] = [
    { label: 'Edit', action: 'edit', icon: 'fas fa-edit' },
    { label: 'Delete', action: 'delete', icon: 'fas fa-trash' }
  ];

  currentEditingFeature: FeatureCard | null = null;
  openDropdownId: string | null = null;

  sections: FeatureSection[] = [
    {
      id: 'must-have',
      title: 'Mo',
      subtitle: 'MUST HAVE',
      features: [
        {
          id: 'must-1',
          title: 'Contactless Payment Capability',
          description: 'Allows user to make contactless payments using the biometric card Allows user to make contactless payments using the biometric cardAllows user to make contactless payments using the biometric cardAllows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        },
        {
          id: 'must-2',
          title: 'Secure Payment Processing',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        }
      ]
    },
    {
      id: 'should-have',
      title: 'S',
      subtitle: 'SHOULD HAVE',
      features: [
        {
          id: 'should-1',
          title: 'Contactless Payment Capability',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        },
        {
          id: 'should-2',
          title: 'Secure Payment Processing',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        }
      ]
    },
    {
      id: 'could-have',
      title: 'Co',
      subtitle: 'COULD HAVE',
      features: [
        {
          id: 'could-1',
          title: 'Contactless Payment Capability',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        }
      ]
    },
    {
      id: 'wont-have',
      title: 'W',
      subtitle: 'WON\'T HAVE',
      features: [
        {
          id: 'wont-1',
          title: 'Contactless Payment Capability',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        },
        {
          id: 'wont-2',
          title: 'Secure Payment Processing',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        }
      ]
    }
  ];

  constructor() {}

  getSectionIds(): string[] {
    return this.sections.map(section => section.id);
  }

  onNext(): void {
    // Handle next button click
  }

  onDrop(event: CdkDragDrop<FeatureCard[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    }
  }

  addNewFeature(sectionId: string): void {
    const section = this.sections.find(s => s.id === sectionId);
    if (section) {
      const newId = `${sectionId}-${section.features.length + 1}`;
      section.features.push({
        id: newId,
        title: 'New Feature',
        description: 'Add description here',
        tags: ['Tag']
      });
    }
  }

  deleteFeature(sectionId: string, featureId: string): void {
    const section = this.sections.find(s => s.id === sectionId);
    if (section) {
      section.features = section.features.filter(f => f.id !== featureId);
    }
  }

  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();

    // If clicking on the same dropdown, close it
    if (this.openDropdownId === featureId) {
      this.openDropdownId = null;
    } else {
      // Open the clicked dropdown and close others
      this.openDropdownId = featureId;
    }
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  // Dialog methods
  openEditDialog(feature: FeatureCard): void {
    this.currentEditingFeature = feature;
    this.dialogConfig.title = `Edit Feature: ${feature.title}`;
    this.isDialogVisible = true;
  }

  closeDialog(): void {
    this.isDialogVisible = false;
    this.currentEditingFeature = null;
  }

  onDialogDropdownAction(action: string): void {
    if (!this.currentEditingFeature) return;

    switch (action) {
      case 'edit':
        console.log('Edit feature:', this.currentEditingFeature);
        // Implement edit logic here
        break;
      case 'delete':
        this.deleteFeatureById(this.currentEditingFeature.id);
        this.closeDialog();
        break;
      default:
        console.log('Unknown action:', action);
    }
  }

  private deleteFeatureById(featureId: string): void {
    for (const section of this.sections) {
      const featureIndex = section.features.findIndex(f => f.id === featureId);
      if (featureIndex !== -1) {
        section.features.splice(featureIndex, 1);
        break;
      }
    }
  }

  // Method to open dialog from card menu
  openDialogFromCard(feature: FeatureCard): void {
    this.openEditDialog(feature);
  }

  ngOnInit(): void {
    // Add click listener to close dropdowns when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown')) {
        this.closeAllDropdowns();
      }
    });
  }
}