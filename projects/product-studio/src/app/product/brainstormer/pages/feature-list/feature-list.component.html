<div class="container-fluid p-3" (click)="closeAllDropdowns()">
  <div class="row g-3">
    <!-- g-3 provides gap between columns -->
    <div
      *ngFor="let section of sections"
      class="col-12 col-sm-6 col-lg-3 d-flex flex-column"
    >
      <!-- Section Main Header (Mo, S, Co, W) -->
      <div
        class="section-header text-center text-white rounded-top"
        [id]="section.id + '-header'"
      >
        <div class="section-title typography">
          {{ section.title }}
        </div>
      </div>

      <!-- Section Subtitle Header (MUST HAVE, SHOULD HAVE, etc.) -->
      <div
        class="section-subtitle-header text-center mb-3"
        [id]="section.id + '-subtitle-header'"
      >
        <div class="section-subtitle-text">
          {{ section.subtitle.toLocaleUpperCase() }}
        </div>
      </div>

      <!-- Drop Zone for Features -->
      <div
        cdkDropList
        [id]="section.id"
        [cdkDropListData]="section.features"
        [cdkDropListConnectedTo]="getSectionIds()"
        (cdkDropListDropped)="onDrop($event)"
        class="feature-list-dropzone border border-top-0 p-2 d-flex flex-column gap-3 flex-grow-1"
      >
        <!-- Feature Cards using awe-card -->
        <awe-card
          *ngFor="let feature of section.features"
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="feature-item-card"
          cdkDrag
          (cdkDragStarted)="
            $event.source.element.nativeElement.style.cursor = 'grabbing'
          "
          (cdkDragEnded)="
            $event.source.element.nativeElement.style.cursor = 'grab'
          "
        >
          <!-- Projected Header for awe-card -->
          <div
            awe-card-header-content
            class="d-flex justify-content-between align-items-center"
          >
            <awe-heading
              variant="s2"
              type="bold"
              class="feature-title mb-0 flex-grow-1 pe-2"
              >{{ feature.title }}</awe-heading
            >

               <awe-icons
               (click)="toggleDropdown(feature.id, $event)"
                [iconName]="'three-dot'"
                [iconColor]="'blue'"
              ></awe-icons>
             <div class="dropdown position-relative">
              <div class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen(feature.id)">
                <button class="dropdown-item border-buttom" type="button" (click)="openEditDialog(feature); closeAllDropdowns()">Edit</button>
                <button class="dropdown-item text-danger" type="button" (click)="deleteFeature(section.id, feature.id); closeAllDropdowns()">
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Projected Body for awe-card (Default Slot) -->
          <p class="feature-description text-muted small mb-3 flex-grow-1">
            {{ feature.description }}
          </p>
          <div class="d-flex flex-wrap gap-1">
            <span
              *ngFor="let tag of feature.tags"
              class="feature-tag badge rounded-pill px-2 py-1"
            >
              {{ tag }}
            </span>
          </div>
        </awe-card>

        <!-- Empty State for Drop Zone -->
        <div
          *ngIf="section.features.length === 0"
          class="text-center text-muted fst-italic py-4"
        >
          Drag and drop features here
        </div>
      </div>

      <!-- Add More Button -->
      <div class="add-more-section text-center">
        <button class="add-more-btn w-100" (click)="addNewFeature(section.id)">
          Add more <span class="plus-icon">+</span>
        </button>
      </div>
    </div>
  </div>
</div>
