// Using CSS custom properties instead of SCSS variables
// Minimal custom SCSS - most styling handled by Bootstrap classes
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@900&display=swap');


// General container styling (from your image, it's white/light gray)
// Section Headers
.section-header {
  height: 100px; // As per image
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  // rounded-top is a Bootstrap class

  .section-title.typography {
    font-family: "Inter", sans-serif;
    font-size: 96px;
    font-weight: 900; // For the thick letters like MO, S
    line-height: 1; // Adjust for better vertical centering
    text-transform: uppercase;
    letter-spacing: -2px; // Tighten spacing
    margin-top: 15%;
  }
}

.section-subtitle-header {
  height: 42px; // As per image
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color, #ffffff); // White bar

  .section-subtitle-text {
    font-family: "Inter", sans-serif;
    font-size: 32px; // Smaller than main title
    font-weight: 700;
    line-height: 1;
    text-transform: uppercase;
    color: #A2A2A2;
  }
}

// Section-specific header background colors
#must-have-header {
  background-color: #808080 !important;
}
#should-have-header {
  background-color: #808080 !important;
}
#could-have-header {
  background-color: #808080 !important;
}
#wont-have-header {
  background-color: #808080 !important;
}

// Dropzone for features
.feature-list-dropzone {

  border-color: #dee2e6 !important;
}

app-feature-list awe-card.feature-item-card {
  // Override awe-card CSS variables if needed for this specific context
  --awe-card-background: #fbfbfb; // Light background
  --awe-card-border: 1px solid #e9ecef; // Lighter border than awe-card default
  --awe-card-box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1); // Softer shadow
  --awe-card-border-radius: 0.5rem; // 8px, Bootstrap's rounded is 0.375rem
  --awe-card-padding-base: 1rem; // p-3 in Bootstrap is 1rem

  min-height: 185px; // From your original .feature-card
  display: flex; // Ensure it behaves as a flex container if its content needs to grow
  flex-direction: column; // Stack header/body/footer

  &:hover {
    --awe-card-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); 
    transform: translateY(-2px); 
  }

  
    font-family: "Mulish", sans-serif; 
    font-weight: 600; 
    color: var(--body-text-color, #333333);
    font-size: 1rem; 

  .feature-description {
    font-family: "Mulish", sans-serif;
    color: var(--body-text-color, #666666);
    font-size: 0.875rem; // small text
    line-height: 1.5;
  }

  .feature-tag {
    font-family: "Mulish", sans-serif;
    background-color: #e9ecef;
    color: #495057;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .action-button {
    img.action-icon {
      width: 20px; // Adjust icon size if needed
      height: 20px;
    }
  }
}

// Dropdown Menu (projected into awe-card header)
.dropdown {
    position: relative;
    height: 44px;

    .dropdown-menu {
      button {
        min-width: 0;
        padding: 0.5rem 1rem;
        background-color: none;
        border: none;
      }
        display: none;
        position: absolute;
        top: 55%;
        right: 20%;
        min-width: 120px;
        background-color: #fff;
        // border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        // padding: 0.5rem 0;
        z-index: 1000;
        &.show {
            display: block;
        }
        .dropdown-item {
            display: block;
            width: 100%;
            // border-bottom: 1px solid #303233;
            // padding: 0.375rem 1rem;
            clear: both;
            font-weight: 400;
            color: #212529;
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            // border: 0;
            transition: background-color 0.15s ease-in-out;

            &:hover,
            &:focus {
                background-color: #f8f9fa;
                color: #1e2125;
            }

            &.text-danger:hover {
                background-color: #f5c2c7;
                color: #842029;
            }
        }
    }
}


.add-more-section {
  background-color: #e9ecef;
  .add-more-btn {
    background-color: transparent; 
    border: none;
    color: var(--body-text-color, #495057);
    font-family: "Mulish", sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    padding: 0.75rem 1rem; 
    height: 48px; 
    display: flex;
    align-items: center;
    justify-content: center;

    .plus-icon {
      font-size: 20px;
      margin-left: 0.5rem;
    }
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.5rem;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(2deg);
  
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: rgba(108, 117, 125, 0.1);
  border: 2px dashed #6c757d;
  border-radius: 0.5rem; 
  min-height: 185px; 
  
  transition: all 0.3s ease;
}

.cdk-drag-animating {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.feature-list-dropzone.cdk-drop-list-dragging
  awe-card.feature-item-card:not(.cdk-drag-placeholder) {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

:host ::ng-deep awe-card.cdk-drag-dragging {
  cursor: grabbing !important;
}
