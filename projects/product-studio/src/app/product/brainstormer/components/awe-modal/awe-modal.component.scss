// Import your _variables.scss if you have one with theme colors
// @import 'path/to/your/variables';

// Define default CSS variables for the modal
:host {
  --awe-modal-backdrop-bg: rgba(0, 0, 0, 0.5);
  --awe-modal-content-bg: white;
  --awe-modal-content-color: #333;
  --awe-modal-border-radius: 8px;
  --awe-modal-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  --awe-modal-header-padding: 1rem;
  --awe-modal-body-padding: 1rem;
  --awe-modal-footer-padding: 1rem;
  --awe-modal-header-border-bottom: 1px solid #e9ecef;
  --awe-modal-footer-border-top: 1px solid #e9ecef;
  --awe-modal-close-button-color: #6c757d;
  --awe-modal-z-index-backdrop: 1050;
  --awe-modal-z-index-content: 1051;
  --awe-modal-animation-duration: 0.3s; // Base duration
}

.awe-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--awe-modal-backdrop-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--awe-modal-z-index-backdrop);
  opacity: 0;
  visibility: hidden;
  backdrop-filter: blur(3px); // Background blur

  &.animating {
    transition: opacity var(--awe-modal-animation-duration) ease-out,
                visibility var(--awe-modal-animation-duration) ease-out;
  }

  &.awe-modal-open {
    opacity: 1;
    visibility: visible;
  }
  &.awe-modal-closing {
     opacity: 0;
     visibility: hidden;
  }
}

.awe-modal-content {
  background-color: var(--awe-modal-content-bg);
  color: var(--awe-modal-content-color);
  border-radius: var(--awe-modal-border-radius);
  box-shadow: var(--awe-modal-box-shadow);
  display: flex;
  flex-direction: column;
  overflow: hidden; // For border-radius on children
  z-index: var(--awe-modal-z-index-content);
  transform: scale(0.95); // Initial state for some animations
  opacity: 0; // Initial state for fade

  .awe-modal-backdrop.animating & { // Apply transition when backdrop is animating
    transition: transform var(--awe-modal-animation-duration) ease-out,
                opacity var(--awe-modal-animation-duration) ease-out;
  }
  .awe-modal-backdrop.awe-modal-open & {
    transform: scale(1);
    opacity: 1;
  }
   .awe-modal-backdrop.awe-modal-closing & {
    transform: scale(0.95);
    opacity: 0;
  }


  // Positioning
  &.position-center {
    // Default, handled by backdrop flex align/justify
  }
  .awe-modal-backdrop.position-left & { // If backdrop controls position via flex
    margin-right: auto; // Pushes content to the left
  }
  .awe-modal-backdrop.position-right & {
    margin-left: auto; // Pushes content to the right
  }
  // For top/bottom, backdrop would need align-items: flex-start/flex-end
  .awe-modal-backdrop.position-top & {
    margin-bottom: auto;
  }
  .awe-modal-backdrop.position-bottom & {
    margin-top: auto;
  }

  // Alternatively, for absolute positioning of modal within backdrop:
  // &.position-left { position: absolute; left: 0; top: 50%; transform: translateY(-50%); }
  // &.position-right { position: absolute; right: 0; top: 50%; transform: translateY(-50%); }
  // &.position-top { position: absolute; top: 0; left: 50%; transform: translateX(-50%); }
  // &.position-bottom { position: absolute; bottom: 0; left: 50%; transform: translateX(-50%); }
}

.awe-modal-header {
  padding: var(--awe-modal-header-padding);
  border-bottom: var(--awe-modal-header-border-bottom);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;

  .awe-modal-default-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
    flex-grow: 1;
  }
}

.awe-modal-close-button {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
  color: var(--awe-modal-close-button-color);
  opacity: 0.7;
  padding: 0.5rem; // Make it easier to click
  margin: -0.5rem; // Offset padding
  cursor: pointer;

  &:hover {
    opacity: 1;
  }
}

.awe-modal-body {
  padding: var(--awe-modal-body-padding);
  overflow-y: auto; // Scroll for long content
  flex-grow: 1; // Takes available space
}

.awe-modal-footer {
  padding: var(--awe-modal-footer-padding);
  border-top: var(--awe-modal-footer-border-top);
  display: flex;
  justify-content: flex-end; // Common for buttons
  align-items: center;
  flex-shrink: 0;

  // Example: Spacing for projected buttons
  ::ng-deep > *:not(:last-child) {
    margin-right: 0.5rem;
  }
}

// --- Animations ---
// Fade (applied by default if transform changes)

// Slide-in animations (Example)
@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}
@keyframes slideOutLeft {
  from { transform: translateX(0); opacity: 1; }
  to { transform: translateX(-100%); opacity: 0; }
}
// Similar for right, top, bottom

.awe-modal-content.animation-slide-in-left {
  .awe-modal-backdrop.awe-modal-open & { animation: slideInLeft var(--awe-modal-animation-duration) ease-out forwards; }
  .awe-modal-backdrop.awe-modal-closing & { animation: slideOutLeft var(--awe-modal-animation-duration) ease-in forwards; }
}
// ... Add other .animation-* classes and their keyframes ...

@keyframes zoomIn {
  from { transform: scale(0.5); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
@keyframes zoomOut {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.5); opacity: 0; }
}
.awe-modal-content.animation-zoom {
  transform-origin: center center; // Important for zoom
  .awe-modal-backdrop.awe-modal-open & { animation: zoomIn var(--awe-modal-animation-duration) ease-out forwards; }
  .awe-modal-backdrop.awe-modal-closing & { animation: zoomOut var(--awe-modal-animation-duration) ease-in forwards; }
}

// Reset transform for non-transform animations if backdrop uses transform for centering
.awe-modal-content.animation-fade {
  .awe-modal-backdrop.awe-modal-open &, .awe-modal-backdrop.awe-modal-closing & {
    // Opacity is handled by the main .awe-modal-content transition
    // transform: scale(1) !important; // Keep scale at 1 if using fade
  }
}