import { Component, Input, OnInit, Output, EventEmitter, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, state, style, animate, transition, keyframes, query, stagger } from '@angular/animations';
// import { createLogger } from '../../../core/utils/logger';
import {createLogger} from '../../utils/logger';

@Component({
  selector: 'app-error-page',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './error-page.component.html',
  styleUrls: ['./error-page.component.scss'],
  animations: [
    trigger('eyesBlink', [
      state('open', style({
        transform: 'scaleY(1)'
      })),
      state('closed', style({
        transform: 'scaleY(0.1)'
      })),
      transition('open <=> closed', animate('300ms ease-in-out'))
    ]),
    trigger('robotShake', [
      state('idle', style({
        transform: 'translateX(0)'
      })),
      state('shaking', style({
        transform: 'translateX(0)'
      })),
      transition('idle => shaking', [
        animate('500ms ease-in-out', keyframes([
          style({ transform: 'translateX(0)', offset: 0 }),
          style({ transform: 'translateX(-5px)', offset: 0.1 }),
          style({ transform: 'translateX(5px)', offset: 0.2 }),
          style({ transform: 'translateX(-5px)', offset: 0.3 }),
          style({ transform: 'translateX(5px)', offset: 0.4 }),
          style({ transform: 'translateX(-5px)', offset: 0.5 }),
          style({ transform: 'translateX(5px)', offset: 0.6 }),
          style({ transform: 'translateX(-5px)', offset: 0.7 }),
          style({ transform: 'translateX(5px)', offset: 0.8 }),
          style({ transform: 'translateX(-5px)', offset: 0.9 }),
          style({ transform: 'translateX(0)', offset: 1 })
        ]))
      ])
    ]),
    trigger('fadeInUp', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('500ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ]),
    trigger('errorTextAnimation', [
      transition(':enter', [
        query('.error-char-container', [
          style({ opacity: 0, transform: 'translateY(20px)' }),
          stagger(100, [
            animate('500ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true })
      ])
    ]),
    trigger('pulseAnimation', [
      state('normal', style({
        transform: 'scale(1)'
      })),
      state('pulse', style({
        transform: 'scale(1)'
      })),
      transition('normal => pulse', [
        animate('1000ms ease-in-out', keyframes([
          style({ transform: 'scale(1)', offset: 0 }),
          style({ transform: 'scale(1.05)', offset: 0.5 }),
          style({ transform: 'scale(1)', offset: 1 })
        ]))
      ])
    ]),
    trigger('rotateAnimation', [
      state('normal', style({
        transform: 'rotate(0deg)'
      })),
      state('rotating', style({
        transform: 'rotate(360deg)'
      })),
      transition('normal => rotating', [
        animate('1000ms ease-in-out')
      ])
    ])
  ]
})
export class ErrorPageComponent implements OnInit, OnDestroy {

  @Input() errorDescription: string = '';
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() terminalOutput: string = '';
  @Input() showTerminal: boolean = false;
  @Input() progressState: string = '';

  @Output() retry = new EventEmitter<void>();
  @Output() goHome = new EventEmitter<void>();
  @Output() showDetails = new EventEmitter<void>();

  eyesState: 'open' | 'closed' = 'open';
  robotState: 'idle' | 'shaking' = 'idle';
  pulseState: 'normal' | 'pulse' = 'normal';
  rotateState: 'normal' | 'rotating' = 'normal';

  // Track retry attempts
  retryCount: number = 0;
  homeButtonEnabled: boolean = false;

  // ERROR letters with their animation states
  errorLetters: { char: string, state: 'normal' | 'hovered', animationTrigger: string }[] = [];

  // Animated error description
  animatedDescription: string = '';
  private typingInterval: any;
  private blinkInterval: any;
  private shakeInterval: any;
  private pulseInterval: any;
  private rotateInterval: any;

  // Logger instance
  private logger = createLogger('ErrorPageComponent');

  constructor() { }

  ngOnInit(): void {
    // Initialize the ERROR letters array
    this.errorLetters = [
      { char: 'E', state: 'normal', animationTrigger: '' },
      { char: 'R', state: 'normal', animationTrigger: '' },
      { char: 'R', state: 'normal', animationTrigger: '' },
      { char: 'O', state: 'normal', animationTrigger: '' },
      { char: 'R', state: 'normal', animationTrigger: '' }
    ];

    // Start the animations
    this.startBlinking();
    this.startShaking();
    this.startPulsing();
    this.startRotating();

    // Start typing animation for error description
    this.startTypingAnimation();
  }

  ngOnDestroy(): void {
    // Clear all intervals to prevent memory leaks
    if (this.blinkInterval) clearInterval(this.blinkInterval);
    if (this.shakeInterval) clearInterval(this.shakeInterval);
    if (this.pulseInterval) clearInterval(this.pulseInterval);
    if (this.rotateInterval) clearInterval(this.rotateInterval);
    if (this.typingInterval) clearInterval(this.typingInterval);
  }

  startBlinking(): void {
    // Blink every 3 seconds
    this.blinkInterval = setInterval(() => {
      this.eyesState = 'closed';
      setTimeout(() => {
        this.eyesState = 'open';
      }, 300);
    }, 3000);
  }

  startShaking(): void {
    // Shake the robot every 5 seconds
    this.shakeInterval = setInterval(() => {
      this.robotState = 'shaking';
      setTimeout(() => {
        this.robotState = 'idle';
      }, 500);
    }, 5000);
  }

  startPulsing(): void {
    // Pulse animation every 3 seconds
    this.pulseInterval = setInterval(() => {
      this.pulseState = 'pulse';
      setTimeout(() => {
        this.pulseState = 'normal';
      }, 1000);
    }, 3000);
  }

  startRotating(): void {
    // Rotate animation every 4 seconds
    this.rotateInterval = setInterval(() => {
      this.rotateState = 'rotating';
      setTimeout(() => {
        this.rotateState = 'normal';
      }, 1000);
    }, 4000);
  }

  startTypingAnimation(): void {
    // Clear any existing interval
    if (this.typingInterval) clearInterval(this.typingInterval);

    // Reset the animated description
    this.animatedDescription = '';

    // Get the full description
    const fullDescription = this.errorDescription;
    let charIndex = 0;

    // Start typing animation
    this.typingInterval = setInterval(() => {
      if (charIndex < fullDescription.length) {
        this.animatedDescription += fullDescription.charAt(charIndex);
        charIndex++;
      } else {
        // Stop the interval when done
        clearInterval(this.typingInterval);
      }
    }, 30); // Adjust speed as needed
  }

  onLetterMouseEnter(index: number): void {
    this.errorLetters[index].state = 'hovered';
  }

  onLetterMouseLeave(index: number): void {
    this.errorLetters[index].state = 'normal';
  }

  onRetryClick(): void {
    // Increment retry counter
    this.retryCount++;

    // Enable home button after 3 retries
    if (this.retryCount >= 3 && !this.homeButtonEnabled) {
      this.homeButtonEnabled = true;

      // Celebrate by animating all letters at once
      this.celebrateHomeButtonEnabled();
    }

    // Trigger shake animation before emitting retry event
    this.robotState = 'shaking';
    setTimeout(() => {
      this.robotState = 'idle';
      this.retry.emit();
    }, 500);
  }

  /**
   * Celebrate when the home button becomes enabled by animating all letters
   */
  celebrateHomeButtonEnabled(): void {
    // Animate each letter in sequence
    this.errorLetters.forEach((letter, index) => {
      setTimeout(() => {
        // Set letter to hovered state
        letter.state = 'hovered';

        // Reset after animation
        setTimeout(() => {
          letter.state = 'normal';
        }, 500);
      }, index * 200); // Stagger the animations
    });
  }

  onGoHomeClick(): void {
    // Only allow going home if the button is enabled (after 3 retries)
    if (this.homeButtonEnabled) {
      this.goHome.emit();
    } else {
      // Shake animation to indicate button is disabled
      this.robotState = 'shaking';
      setTimeout(() => {
        this.robotState = 'idle';
      }, 500);
    }
  }

  onShowDetailsClick(): void {
    this.showDetails.emit();
    this.showTerminal = !this.showTerminal;
  }

  // Parse and format JSON error messages for better display
  parseErrorMessage(message: string): string {
    try {
      if (message && message.includes('{') && message.includes('}')) {
        const jsonStartIndex = message.indexOf('{');
        const jsonEndIndex = message.lastIndexOf('}') + 1;
        const jsonPart = message.substring(jsonStartIndex, jsonEndIndex);
        const parsedData = JSON.parse(jsonPart);

        if (parsedData.message) {
          return parsedData.message;
        }
      }
      return message;
    } catch (e) {
      return message;
    }
  }
}
