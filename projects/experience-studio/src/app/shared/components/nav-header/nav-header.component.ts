import { Component, OnInit, Input, Output, EventEmitter, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent, HeaderComponent, HeadingComponent } from '@awe/play-comp-library';

import { ThemeService } from '../../services/theme-service/theme.service';
import { UserProfile } from '../../models/user-profile.model';
import { AppConstants } from '../../appConstants';

@Component({
  selector: 'app-nav-header',
  imports: [HeaderComponent, CommonModule, ButtonComponent, HeadingComponent],
  standalone: true,
  templateUrl: './nav-header.component.html',
  styleUrl: './nav-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NavHeaderComponent implements OnInit {
  @Output() logout = new EventEmitter<void>();
  @Input() userProfile?: UserProfile;
  showProfileMenu = false;
  themeToggleIcon = '';
  themeMenuIcon = '';
  logoSrc = '';

  constructor(private themeService: ThemeService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.updateThemeAssets();
    new MutationObserver(() => this.updateThemeAssets()).observe(document.body, {
      attributes: true,
      attributeFilter: ['class'],
    });
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
    this.updateThemeAssets();
  }

  toggleProfileMenu(): void {
    this.showProfileMenu = !this.showProfileMenu;
    this.cdr.markForCheck();
  }

  onLogout(): void {
    this.logout.emit();
    this.showProfileMenu = false;
    this.cdr.markForCheck();
  }

  getProfileImage(): string {
    return this.userProfile?.photoUrl || `${AppConstants.AssetsPath}/user-avatar.svg`;
  }

  getDisplayName(): string {
    return this.userProfile?.displayName || 'User';
  }

  getEmail(): string {
    return this.userProfile?.mail || this.userProfile?.userPrincipalName || '';
  }

  private updateThemeAssets(): void {
    const currentTheme = this.themeService.getCurrentTheme();
    this.logoSrc = `${AppConstants.AssetsPath}/ascendion-logo-${currentTheme}.svg`;
    this.themeToggleIcon = `${AppConstants.AssetsPath}/theme-toggle-${currentTheme}.svg`;
    this.themeMenuIcon = `${AppConstants.AssetsPath}/menu-${currentTheme}.svg`;
    this.cdr.markForCheck();
  }
}
