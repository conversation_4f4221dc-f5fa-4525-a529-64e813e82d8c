<div class="vertical-stepper" [ngClass]="theme">
  <div class="stepper-container">
    <div
      class="stepper-item"
      *ngFor="let step of steps; let i = index; let isLast = last"
      [ngClass]="getStepStatus(i)"
      [class.hidden]="!shouldShowStep(i)"
      [class.collapsed]="isStepCollapsed(i)"
      [class.in-progress-mode]="status === 'IN_PROGRESS' && isProcessingStep(i)">
      <!-- Connector Line -->
      <div class="step-line-container" *ngIf="!isLast" [class.hidden-line]="isStepCollapsed(i)">
        <div
          class="step-line"
          [class.completed]="getStepStatus(i) === 'completed'"
          [class.animating]="isLineAnimating(i)"
          [class.expanding]="!isStepCollapsed(i)"></div>
      </div>

      <!-- Circle -->
      <div class="step-circle"
           [class.active]="getStepStatus(i) === 'active' && !step.completed && !isFailureStep(step)"
           [class.failed]="(status === 'FAILED' && (i === currentStepIndex || step.title === 'Build Failed')) || isFailureStep(step) || step.title === this.getDisplayTitleForProgress(StepperState.BUILD_FAILED)"
           [class.processing]="status === 'IN_PROGRESS' && currentStepIndex >= 0 && steps[currentStepIndex] && !steps[currentStepIndex].completed && !isFailureStep(step) && step.title !== this.getDisplayTitleForProgress(StepperState.BUILD_FAILED)"
           [class.clickable]="isProcessedStep(i) || status !== 'IN_PROGRESS'"
           (click)="isProcessingStep(i) ? null : toggleStepCollapse(i)">
        <!-- Checkmark icon for completed steps -->
        <svg
          class="step-icon"
          *ngIf="(getStepStatus(i) === 'completed' || step.completed) && !isFailureStep(step) && step.title !== getDisplayTitleForProgress(StepperState.BUILD_FAILED)"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z" fill="url(#paint0_linear_tick)"/>
          <mask id="mask0_tick" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
            <rect width="24" height="24" fill="#D9D9D9"/>
          </mask>
          <g mask="url(#mask0_tick)">
            <path d="M16.6937 6.37109L18.9977 8.2911L15.2477 12.7911L11.4977 17.2911L10.4462 18.5526L9.28516 17.3916L4.78516 12.8916L6.90616 10.7706L10.2437 14.1096L16.6937 6.37109Z" fill="white"/>
          </g>
          <defs>
            <linearGradient id="paint0_linear_tick" x1="0" y1="12" x2="24" y2="12" gradientUnits="userSpaceOnUse">
              <stop stop-color="#6566CD"/>
              <stop offset="1" stop-color="#E30A6D"/>
            </linearGradient>
          </defs>
        </svg>

        <!-- X icon for failed steps -->
        <svg
          class="step-icon"
          *ngIf="isFailureStep(step) || (status === 'FAILED' && i === currentStepIndex) || step.title === getDisplayTitleForProgress(StepperState.BUILD_FAILED)"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z" fill="url(#paint0_linear_x)"/>
          <mask id="mask0_x" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
            <rect width="24" height="24" fill="#D9D9D9"/>
          </mask>
          <g mask="url(#mask0_x)">
            <path d="M16.2426 8.87868L15.1213 7.75736L12 10.8787L8.87868 7.75736L7.75736 8.87868L10.8787 12L7.75736 15.1213L8.87868 16.2426L12 13.1213L15.1213 16.2426L16.2426 15.1213L13.1213 12L16.2426 8.87868Z" fill="white"/>
          </g>
          <defs>
            <linearGradient id="paint0_linear_x" x1="0" y1="12" x2="24" y2="12" gradientUnits="userSpaceOnUse">
              <stop stop-color="#6566CD"/>
              <stop offset="1" stop-color="#E30A6D"/>
            </linearGradient>
          </defs>
        </svg>
        <!-- Modern Loading Spinner -->
        <div
          class="modern-loading-spinner"
          *ngIf="getStepStatus(i) === 'active' && !step.completed && status !== 'FAILED' && !isFailureStep(step) && step.title !== getDisplayTitleForProgress(StepperState.BUILD_FAILED)">
          <div class="spinner-ring"></div>
          <div class="spinner-core"></div>
        </div>
        <span
          class="step-number"
          *ngIf="getStepStatus(i) !== 'completed' && getStepStatus(i) !== 'active' && !step.completed">
          {{ i + 1 }}
        </span>
      </div>

      <!-- Content -->
      <div class="step-content">
        <!-- Next step (no shimmer) -->
        <div class="step-next" *ngIf="getStepStatus(i) === 'next'">
          <div class="next-title">{{ formatTitle(step.title) }}</div>
        </div>

        <!-- Actual content for completed or active steps -->
        <div *ngIf="getStepStatus(i) !== 'next'" class="step-content-inner">
          <!-- Title is always visible and clickable (except when processing) -->
          <h3 class="step-title"
              [class.clickable]="isProcessedStep(i) || status !== 'IN_PROGRESS'"
              (click)="isProcessingStep(i) ? null : toggleStepCollapse(i)">
            <span class="step-title-text">{{ formatTitle(step.title) }}</span>

            <!-- Timer display at the right end of the title -->
            <div class="step-timer" *ngIf="step.timerActive && step.elapsedTime !== undefined">
              {{ formatElapsedTime(step.elapsedTime || 0) }}
            </div>

            <!-- Retry button for failed steps -->
            <button
              *ngIf="shouldShowRetryButton(step, i)"
              class="step-retry-button"
              (click)="onRetryClick(i, $event)"
              [title]="'Retry attempt ' + (step.retryCount || 0) + '/3'">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4.01 7.58 4.01 12C4.01 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z" fill="currentColor"/>
              </svg>
            </button>
          </h3>
          <!-- Description collapses independently -->
          <div class="step-description"
               [class.collapsed]="isStepCollapsed(i)"
               [class.typing]="step.isTyping"
               [class.shimmer]="step.isRetrying">
            <markdown [data]="step.visibleDescription || step.description"></markdown>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Restart button (only shown when restartable is true) -->
  <div class="restart-button-container" *ngIf="restartable">
    <button class="restart-button" (click)="restartStepper()">Restart Process</button>
  </div>
</div>
