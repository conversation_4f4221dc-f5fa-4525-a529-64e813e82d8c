/**
 * Debug script to test URL extraction with the exact user response
 * This helps verify the implementation works correctly
 */

// Simple debug function that can be run in browser console
export function debugUrlExtraction() {
  console.log('🔍 Starting URL extraction debug test');

  // Your exact response structure
  const userResponse = {
    status: "COMPLETED" as const,
    log: "Agent : Deployment SUCCESSFUL ",
    progress: "DEPLOY",
    progress_description: "Wohoo! The deployment is completed. Please check the preview screen for the output.",
    history: [],
    metadata: [
      {
        type: "ref_code" as const,
        data: "https://mlo-a774-5c9a.netlify.app"
      }
    ]
  };

  console.log('📋 Test response:', userResponse);
  console.log('📋 Expected URL to extract:', "https://mlo-a774-5c9a.netlify.app");

  // Test URL validation
  try {
    const testUrl = "https://mlo-a774-5c9a.netlify.app";
    const urlObject = new URL(testUrl);
    const isValidProtocol = urlObject.protocol === 'http:' || urlObject.protocol === 'https:';

    console.log('🔍 URL validation test:', {
      url: testUrl,
      protocol: urlObject.protocol,
      hostname: urlObject.hostname,
      isValidProtocol: isValidProtocol
    });

    if (isValidProtocol && urlObject.hostname) {
      console.log('✅ URL validation would PASS');
    } else {
      console.log('❌ URL validation would FAIL');
    }
  } catch (error) {
    console.log('❌ URL validation would FAIL with error:', error);
  }

  // Test metadata extraction
  const metadata = userResponse.metadata;
  const refCodeMetadata = metadata.find(m => m.type === 'ref_code');

  console.log('🔍 Metadata extraction test:', {
    totalMetadata: metadata.length,
    refCodeFound: !!refCodeMetadata,
    refCodeData: refCodeMetadata?.data,
    refCodeType: typeof refCodeMetadata?.data
  });

  if (refCodeMetadata && typeof refCodeMetadata.data === 'string') {
    console.log('✅ Metadata extraction would PASS');
    console.log('🎯 URL that would be extracted:', refCodeMetadata.data);
  } else {
    console.log('❌ Metadata extraction would FAIL');
  }

  // Test state validation
  const isCorrectProgress = userResponse.progress === 'DEPLOY';
  const isCorrectStatus = userResponse.status === 'COMPLETED';

  console.log('🔍 State validation test:', {
    progress: userResponse.progress,
    status: userResponse.status,
    isCorrectProgress: isCorrectProgress,
    isCorrectStatus: isCorrectStatus,
    bothCorrect: isCorrectProgress && isCorrectStatus
  });

  if (isCorrectProgress && isCorrectStatus) {
    console.log('✅ State validation would PASS');
  } else {
    console.log('❌ State validation would FAIL');
  }

  return {
    response: userResponse,
    expectedUrl: "https://mlo-a774-5c9a.netlify.app",
    validationResults: {
      urlValid: true,
      metadataValid: !!refCodeMetadata && typeof refCodeMetadata.data === 'string',
      stateValid: isCorrectProgress && isCorrectStatus
    }
  };
}

// Export for use in browser console
(window as any).debugUrlExtraction = debugUrlExtraction;

console.log('🔧 Debug URL extraction function loaded. Run debugUrlExtraction() in console to test.');
