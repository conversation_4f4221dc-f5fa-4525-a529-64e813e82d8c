import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { UserSignatureService } from '../user-signature.service';
import { createLogger } from '../../utils/logger';
import { cacheHelpers } from '../../interceptors/cache.interceptor';

interface ProjectResponse {
  status_code: number;
  projects: Project[];
}

export interface Project {
  project_id: string;
  project_name: string;
  project_description: string;
  project_type: string;
  last_modified: string;
}

@Injectable({
  providedIn: 'root'
})
export class RecentProjectService {
  private apiUrl = environment.apiUrl;
  private logger = createLogger('RecentProjectService');

  constructor(
    private http: HttpClient,
    private userSignatureService: UserSignatureService
  ) { }

  getUserProjects(userSignature?: string, numProjects: number = 10): Observable<ProjectResponse> {
    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    const params = {
      user_signature: signature,
      num_projects: numProjects.toString()
    };

    // Set a custom cache max age of 2 minutes for recent projects
    // This ensures we don't show stale data for too long, but still benefit from caching
    const context = cacheHelpers.setMaxAge(2 * 60 * 1000); // 2 minutes

    this.logger.info('Getting user projects with signature:', signature);
    return this.http.get<ProjectResponse>(`${this.apiUrl}/project`, {
      params,
      context // Pass the cache context to control caching behavior
    });
  }

  /**
   * Get user projects with cache disabled - use this when you need fresh data
   */
  getUserProjectsFresh(userSignature?: string, numProjects: number = 10): Observable<ProjectResponse> {
    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    const params = {
      user_signature: signature,
      num_projects: numProjects.toString()
    };

    // Disable caching for this request
    const context = cacheHelpers.disableCache();

    this.logger.info('Getting fresh user projects with signature:', signature);
    return this.http.get<ProjectResponse>(`${this.apiUrl}/project`, {
      params,
      context // Pass the context to disable caching
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
  }
}
