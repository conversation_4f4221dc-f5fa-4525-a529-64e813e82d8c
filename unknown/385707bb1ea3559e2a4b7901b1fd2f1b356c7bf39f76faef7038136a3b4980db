import { NgModule, ModuleWithProviders } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoggerService, LoggerConfig, LOGGER_CONFIG, DEFAULT_LOGGER_CONFIG } from '../../utils/logger';

@NgModule({
  imports: [
    CommonModule
  ],
  providers: [
    LoggerService
  ]
})
export class LoggerModule {
  /**
   * Use this method in your root module to provide the LoggerService
   * @param config The logger configuration
   */
  static forRoot(config?: Partial<LoggerConfig>): ModuleWithProviders<LoggerModule> {
    return {
      ngModule: LoggerModule,
      providers: [
        LoggerService,
        {
          provide: LOGGER_CONFIG,
          useValue: config ? { ...DEFAULT_LOGGER_CONFIG, ...config } : DEFAULT_LOGGER_CONFIG
        }
      ]
    };
  }
}
