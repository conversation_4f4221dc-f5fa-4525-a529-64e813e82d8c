import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastComponent, ToastType } from './toast.component';
import { ToastService } from '../../services/toast.service';
import { Subscription } from 'rxjs';

export interface Toast {
  id: number;
  message: string;
  type: ToastType;
  duration: number;
}

@Component({
  selector: 'exp-toast-container',
  standalone: true,
  imports: [CommonModule, ToastComponent],
  template: `
    <div class="toast-container" [ngClass]="position">
      <exp-toast
        *ngFor="let toast of toasts"
        [message]="toast.message"
        [type]="toast.type"
        [duration]="toast.duration"
        (closed)="removeToast(toast.id)">
      </exp-toast>
    </div>
  `,
  styles: [`
    .toast-container {
      position: fixed;
      z-index: 9999;
      display: flex;
      flex-direction: column;
      pointer-events: none;
      gap: 12px;
      max-width: 400px;
      width: 100%;
      padding: 16px;

      & > * {
        pointer-events: auto;
      }
    }

    .top-right {
      top: 0;
      right: 0;
      align-items: flex-end;
    }

    .top-left {
      top: 0;
      left: 0;
      align-items: flex-start;
    }

    .bottom-right {
      bottom: 0;
      right: 0;
      align-items: flex-end;
    }

    .bottom-left {
      bottom: 0;
      left: 0;
      align-items: flex-start;
    }
  `]
})
export class ToastContainerComponent implements OnInit, OnDestroy {
  toasts: Toast[] = [];
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' = 'bottom-right';
  private subscription: Subscription | null = null;
  private nextId = 0;

  constructor(private toastService: ToastService) {}

  ngOnInit(): void {
    this.subscription = this.toastService.toastEvents.subscribe(event => {
      if (event.action === 'add' && event.toast) {
        this.addToast(event.toast);
      } else if (event.action === 'clear') {
        this.clearToasts();
      }

      if (event.position) {
        this.position = event.position;
      }
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  addToast(toast: Omit<Toast, 'id'>): void {
    // Limit to 5 toasts at a time to prevent overwhelming the UI
    if (this.toasts.length >= 5) {
      // Remove the oldest toast
      this.toasts.shift();
    }

    const id = this.nextId++;
    this.toasts.push({ ...toast, id });

  }

  removeToast(id: number): void {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
  }

  clearToasts(): void {
    this.toasts = [];
  }
}
